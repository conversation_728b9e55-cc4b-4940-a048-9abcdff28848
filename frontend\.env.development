# 本地环境
VITE_USER_NODE_ENV = development

# 公共基础路径
VITE_PUBLIC_PATH = ./

# 路由模式
# Optional: hash | history
VITE_ROUTER_MODE = hash

# 打包时是否删除 console
VITE_DROP_CONSOLE = true

# 是否开启 VitePWA
VITE_PWA = false

# 开发环境接口地址
VITE_API_URL =  http://127.0.0.1:5566

# 否开启代理
VITE_HTTP_PROXY = true

# 开发环境跨域代理，支持配置多个
VITE_PROXY = [["/api","http://127.0.0.1:5566"]]

#是否启用mqtt
VITE_MQTT = false

# 缓存优化配置
VITE_ENABLE_CACHE_OPTIMIZATION = true
VITE_CACHE_EXPIRY = 1800000
VITE_ENABLE_RESOURCE_PRELOAD = true
VITE_PRELOAD_STRATEGY = smart

# 开发工具配置
VITE_DEVTOOLS = true
VITE_CODEINSPECTOR = true
VITE_REPORT = false

# 构建优化配置
VITE_BUILD_COMPRESS = gzip
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false
