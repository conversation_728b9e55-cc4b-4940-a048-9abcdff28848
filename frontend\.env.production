# 线上环境
VITE_USER_NODE_ENV = production

# 公共基础路径
VITE_PUBLIC_PATH = ./

# 路由模式
# Optional: hash | history
VITE_ROUTER_MODE = hash

# 是否启用 gzip 或 brotli 压缩打包，如果需要多个压缩规则，可以使用 “,” 分隔
# Optional: gzip | brotli | none
VITE_BUILD_COMPRESS = gzip,brotli

# 打包压缩后是否删除源文件
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = true

# 打包时是否删除 console
VITE_DROP_CONSOLE = true

# 是否开启 VitePWA
VITE_PWA = true

# 否开启代理
VITE_HTTP_PROXY = true

# 线上环境接口地址
VITE_API_URL = http://127.0.0.1:5566

# 开发环境跨域代理，支持配置多个
VITE_PROXY = [["/api","http://127.0.0.1:5566"]]

#是否启用mqtt
VITE_MQTT = false

# 缓存优化配置（生产环境）
VITE_ENABLE_CACHE_OPTIMIZATION = true
VITE_CACHE_EXPIRY = 86400000
VITE_ENABLE_RESOURCE_PRELOAD = true
VITE_PRELOAD_STRATEGY = aggressive

# 开发工具配置（生产环境禁用）
VITE_DEVTOOLS = false
VITE_CODEINSPECTOR = false
VITE_REPORT = true
