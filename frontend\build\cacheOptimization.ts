/**
 * Vite 缓存优化插件
 * 提供更精细的缓存控制和优化策略
 */

import { Plugin, ResolvedConfig } from "vite";
import { createHash } from "crypto";
import { existsSync, readFileSync, writeFileSync, mkdirSync } from "fs";
import { join, dirname } from "path";

interface CacheOptimizationOptions {
  // 启用模块缓存
  enableModuleCache?: boolean;
  // 启用依赖缓存
  enableDepsCache?: boolean;
  // 缓存目录
  cacheDir?: string;
  // 缓存过期时间（毫秒）
  cacheExpiry?: number;
  // 是否启用缓存预热
  enableWarmup?: boolean;
  // 预热文件列表
  warmupFiles?: string[];
}

interface CacheEntry {
  hash: string;
  timestamp: number;
  data: any;
  version: string;
}

export function createCacheOptimizationPlugin(options: CacheOptimizationOptions = {}): Plugin {
  const {
    enableModuleCache = true,
    enableDepsCache = true,
    cacheDir = "node_modules/.vite-cache-optimization",
    cacheExpiry = 24 * 60 * 60 * 1000, // 24小时
    enableWarmup = true,
    warmupFiles = []
  } = options;

  let config: ResolvedConfig;
  const moduleCache = new Map<string, CacheEntry>();
  const depsCache = new Map<string, CacheEntry>();

  // 生成文件hash
  function generateFileHash(content: string): string {
    return createHash("md5").update(content).digest("hex");
  }

  // 获取缓存文件路径
  function getCacheFilePath(type: string, key: string): string {
    const safeName = key.replace(/[^a-zA-Z0-9]/g, "_");
    return join(cacheDir, type, `${safeName}.json`);
  }

  // 读取缓存
  function readCache(type: string, key: string): CacheEntry | null {
    try {
      const filePath = getCacheFilePath(type, key);
      if (!existsSync(filePath)) {
        return null;
      }

      const content = readFileSync(filePath, "utf-8");
      const entry: CacheEntry = JSON.parse(content);

      // 检查过期时间
      if (Date.now() - entry.timestamp > cacheExpiry) {
        return null;
      }

      return entry;
    } catch (error) {
      console.warn(`读取缓存失败: ${key}`, error);
      return null;
    }
  }

  // 写入缓存
  function writeCache(type: string, key: string, entry: CacheEntry): void {
    try {
      const filePath = getCacheFilePath(type, key);
      const dir = dirname(filePath);

      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      writeFileSync(filePath, JSON.stringify(entry, null, 2));
    } catch (error) {
      console.warn(`写入缓存失败: ${key}`, error);
    }
  }

  // 预热缓存
  async function warmupCache(): Promise<void> {
    if (!enableWarmup || warmupFiles.length === 0) {
      return;
    }

    console.log("🔥 开始预热Vite缓存...");
    const startTime = Date.now();

    for (const file of warmupFiles) {
      try {
        if (existsSync(file)) {
          const content = readFileSync(file, "utf-8");
          const hash = generateFileHash(content);

          // 预热模块缓存
          if (enableModuleCache) {
            const cacheKey = `module_${file}`;
            const cached = readCache("modules", cacheKey);

            if (!cached || cached.hash !== hash) {
              const entry: CacheEntry = {
                hash,
                timestamp: Date.now(),
                data: { content, file },
                version: "1.0.0"
              };

              moduleCache.set(cacheKey, entry);
              writeCache("modules", cacheKey, entry);
            }
          }
        }
      } catch (error) {
        console.warn(`预热文件失败: ${file}`, error);
      }
    }

    const duration = Date.now() - startTime;
    console.log(`🔥 缓存预热完成，耗时: ${duration}ms`);
  }

  return {
    name: "vite-cache-optimization",

    configResolved(resolvedConfig) {
      config = resolvedConfig;

      // 确保缓存目录存在
      if (!existsSync(cacheDir)) {
        mkdirSync(cacheDir, { recursive: true });
      }
    },

    buildStart() {
      // 构建开始时预热缓存
      if (config.command === "build") {
        warmupCache();
      }
    },

    resolveId(id, importer) {
      // 依赖解析缓存
      if (enableDepsCache && id.includes("node_modules")) {
        const cacheKey = `deps_${id}`;
        const cached = depsCache.get(cacheKey) || readCache("deps", cacheKey);

        if (cached) {
          depsCache.set(cacheKey, cached);
          return cached.data.resolvedId;
        }
      }

      return null;
    },

    load(id) {
      // 模块加载缓存
      if (enableModuleCache) {
        const cacheKey = `module_${id}`;
        const cached = moduleCache.get(cacheKey) || readCache("modules", cacheKey);

        if (cached && existsSync(id)) {
          try {
            const currentContent = readFileSync(id, "utf-8");
            const currentHash = generateFileHash(currentContent);

            // 如果文件未变化，返回缓存内容
            if (cached.hash === currentHash) {
              return cached.data.content;
            }
          } catch (error) {
            // 文件读取失败，清除缓存
            moduleCache.delete(cacheKey);
          }
        }
      }

      return null;
    },

    transform(code, id) {
      // 转换结果缓存
      if (enableModuleCache && !id.includes("node_modules")) {
        const hash = generateFileHash(code);
        const cacheKey = `transform_${id}`;
        const cached = moduleCache.get(cacheKey) || readCache("transforms", cacheKey);

        if (cached && cached.hash === hash) {
          return cached.data;
        }

        // 缓存转换结果
        const entry: CacheEntry = {
          hash,
          timestamp: Date.now(),
          data: { code, map: null },
          version: "1.0.0"
        };

        moduleCache.set(cacheKey, entry);
        writeCache("transforms", cacheKey, entry);
      }

      return null;
    },

    generateBundle(options, bundle) {
      // 输出bundle信息用于缓存分析
      if (config.command === "build") {
        const bundleInfo = {
          chunks: Object.keys(bundle).length,
          totalSize: Object.values(bundle).reduce((size, chunk) => {
            return size + (typeof chunk === "object" && "code" in chunk ? chunk.code.length : 0);
          }, 0),
          timestamp: Date.now()
        };

        console.log("📦 Bundle信息:", bundleInfo);

        // 保存bundle信息到缓存
        const entry: CacheEntry = {
          hash: generateFileHash(JSON.stringify(bundleInfo)),
          timestamp: Date.now(),
          data: bundleInfo,
          version: "1.0.0"
        };

        writeCache("bundles", "latest", entry);
      }
    },

    closeBundle() {
      // 清理内存缓存
      if (config.command === "build") {
        moduleCache.clear();
        depsCache.clear();
        console.log("🧹 内存缓存已清理");
      }
    }
  };
}

// 默认配置
export const defaultCacheOptimizationOptions: CacheOptimizationOptions = {
  enableModuleCache: true,
  enableDepsCache: true,
  cacheDir: "node_modules/.vite-cache-optimization",
  cacheExpiry: 24 * 60 * 60 * 1000,
  enableWarmup: true,
  warmupFiles: ["src/main.ts", "src/App.vue", "src/routers/index.ts", "src/stores/index.ts"]
};
