/**
 * 资源预加载插件
 * 智能预加载关键资源，提升页面加载速度
 */

import { Plugin, ResolvedConfig } from "vite";

interface ResourcePreloadOptions {
  // 启用关键资源预加载
  enableCriticalPreload?: boolean;
  // 启用路由预加载
  enableRoutePreload?: boolean;
  // 启用字体预加载
  enableFontPreload?: boolean;
  // 启用图片预加载
  enableImagePreload?: boolean;
  // 预加载策略
  preloadStrategy?: "aggressive" | "conservative" | "smart";
  // 关键资源列表
  criticalResources?: string[];
  // 字体文件列表
  fontFiles?: string[];
  // 关键图片列表
  criticalImages?: string[];
}

interface PreloadResource {
  href: string;
  as: string;
  type?: string;
  crossorigin?: string;
  media?: string;
}

export function createResourcePreloadPlugin(options: ResourcePreloadOptions = {}): Plugin {
  const {
    enableCriticalPreload = true,
    enableRoutePreload = true,
    enableFontPreload = true,
    enableImagePreload = false,
    preloadStrategy = "smart",
    criticalResources = [],
    fontFiles = [],
    criticalImages = []
  } = options;

  let config: ResolvedConfig;
  const preloadResources: PreloadResource[] = [];

  // 生成预加载标签
  function generatePreloadTags(): string {
    return preloadResources
      .map(resource => {
        const attrs = [`rel="preload"`, `href="${resource.href}"`, `as="${resource.as}"`];

        if (resource.type) {
          attrs.push(`type="${resource.type}"`);
        }
        if (resource.crossorigin) {
          attrs.push(`crossorigin="${resource.crossorigin}"`);
        }
        if (resource.media) {
          attrs.push(`media="${resource.media}"`);
        }

        return `<link ${attrs.join(" ")}>`;
      })
      .join("\n    ");
  }

  // 添加关键CSS预加载
  function addCriticalCSSPreload(): void {
    if (!enableCriticalPreload) return;

    const criticalCSS = ["assets/styles/index.css", "assets/styles/element.css", "assets/styles/common.css"];

    criticalCSS.forEach(href => {
      preloadResources.push({
        href,
        as: "style",
        type: "text/css"
      });
    });
  }

  // 添加字体预加载
  function addFontPreload(): void {
    if (!enableFontPreload) return;

    const defaultFonts = ["assets/fonts/element-icons.woff2", "assets/fonts/iconfont.woff2"];

    const fonts = fontFiles.length > 0 ? fontFiles : defaultFonts;

    fonts.forEach(href => {
      preloadResources.push({
        href,
        as: "font",
        type: "font/woff2",
        crossorigin: "anonymous"
      });
    });
  }

  // 添加关键JavaScript预加载
  function addCriticalJSPreload(): void {
    if (!enableCriticalPreload) return;

    const criticalJS = ["assets/entry/index.js", "assets/vendor/vue-vendor.js", "assets/vendor/element-vendor.js"];

    criticalJS.forEach(href => {
      preloadResources.push({
        href,
        as: "script",
        type: "text/javascript"
      });
    });
  }

  // 添加图片预加载
  function addImagePreload(): void {
    if (!enableImagePreload || criticalImages.length === 0) return;

    criticalImages.forEach(href => {
      preloadResources.push({
        href,
        as: "image"
      });
    });
  }

  // 添加路由预加载
  function addRoutePreload(): void {
    if (!enableRoutePreload) return;

    // 根据策略决定预加载哪些路由
    const routesToPreload = getRoutesToPreload();

    routesToPreload.forEach(route => {
      preloadResources.push({
        href: route,
        as: "script",
        type: "text/javascript"
      });
    });
  }

  // 获取需要预加载的路由
  function getRoutesToPreload(): string[] {
    switch (preloadStrategy) {
      case "aggressive":
        // 激进策略：预加载所有主要路由
        return ["assets/views/home.js", "assets/views/login.js", "assets/views/sys.js", "assets/views/biz.js"];

      case "conservative":
        // 保守策略：只预加载首页
        return ["assets/views/home.js"];

      case "smart":
      default:
        // 智能策略：预加载首页和登录页
        return ["assets/views/home.js", "assets/views/login.js"];
    }
  }

  // 生成模块预加载脚本
  function generateModulePreloadScript(): string {
    return `
    <script>
      // 智能模块预加载
      (function() {
        const preloadModules = [
          '/assets/vendor/utils-vendor.js',
          '/assets/components.js',
          '/assets/stores.js'
        ];

        // 延迟预加载非关键模块
        setTimeout(() => {
          preloadModules.forEach(module => {
            const link = document.createElement('link');
            link.rel = 'modulepreload';
            link.href = module;
            document.head.appendChild(link);
          });
        }, 100);

        // 预加载关键路由
        const criticalRoutes = ${JSON.stringify(getRoutesToPreload())};
        
        // 监听用户交互，预加载可能访问的路由
        let interactionDetected = false;
        
        function preloadOnInteraction() {
          if (interactionDetected) return;
          interactionDetected = true;
          
          criticalRoutes.forEach(route => {
            const link = document.createElement('link');
            link.rel = 'modulepreload';
            link.href = route;
            document.head.appendChild(link);
          });
        }
        
        // 监听用户交互事件
        ['mousedown', 'touchstart', 'keydown'].forEach(event => {
          document.addEventListener(event, preloadOnInteraction, { 
            once: true, 
            passive: true 
          });
        });
        
        // 5秒后自动预加载（兜底策略）
        setTimeout(preloadOnInteraction, 5000);
      })();
    </script>`;
  }

  return {
    name: "vite-resource-preload",

    configResolved(resolvedConfig) {
      config = resolvedConfig;
    },

    transformIndexHtml: {
      enforce: "pre",
      transform(html, context) {
        // 只在生产环境启用
        if (config.command !== "build") {
          return html;
        }

        // 清空之前的预加载资源
        preloadResources.length = 0;

        // 添加各种预加载资源
        addCriticalCSSPreload();
        addFontPreload();
        addCriticalJSPreload();
        addImagePreload();
        addRoutePreload();

        // 添加用户自定义的关键资源
        criticalResources.forEach(href => {
          const extension = href.split(".").pop()?.toLowerCase();
          let as = "fetch";
          let type = "";

          switch (extension) {
            case "css":
              as = "style";
              type = "text/css";
              break;
            case "js":
              as = "script";
              type = "text/javascript";
              break;
            case "woff":
            case "woff2":
              as = "font";
              type = `font/${extension}`;
              break;
            case "png":
            case "jpg":
            case "jpeg":
            case "webp":
              as = "image";
              break;
          }

          preloadResources.push({ href, as, type });
        });

        // 生成预加载标签
        const preloadTags = generatePreloadTags();
        const modulePreloadScript = generateModulePreloadScript();

        // 插入到head标签中
        const headInsert = `
    <!-- 资源预加载 -->
    ${preloadTags}
    
    <!-- 智能模块预加载 -->
    ${modulePreloadScript}`;

        return html.replace(/<head>/, `<head>${headInsert}`);
      }
    },

    generateBundle(options, bundle) {
      // 分析bundle，动态调整预加载策略
      const chunkSizes = Object.entries(bundle)
        .filter(([, chunk]) => chunk.type === "chunk")
        .map(([name, chunk]) => ({
          name,
          size: chunk.type === "chunk" ? chunk.code.length : 0
        }))
        .sort((a, b) => b.size - a.size);

      console.log("📊 Chunk大小分析（前5个）:");
      chunkSizes.slice(0, 5).forEach(chunk => {
        console.log(`  ${chunk.name}: ${(chunk.size / 1024).toFixed(2)}KB`);
      });

      // 如果有大型chunk，建议调整预加载策略
      const largeChunks = chunkSizes.filter(chunk => chunk.size > 100 * 1024); // 100KB
      if (largeChunks.length > 0) {
        console.log("⚠️  发现大型chunk，建议优化:");
        largeChunks.forEach(chunk => {
          console.log(`  ${chunk.name}: ${(chunk.size / 1024).toFixed(2)}KB`);
        });
      }
    }
  };
}

// 默认配置
export const defaultResourcePreloadOptions: ResourcePreloadOptions = {
  enableCriticalPreload: true,
  enableRoutePreload: true,
  enableFontPreload: true,
  enableImagePreload: false,
  preloadStrategy: "smart",
  criticalResources: [],
  fontFiles: [],
  criticalImages: []
};
