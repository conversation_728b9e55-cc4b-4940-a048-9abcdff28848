/**
 * Electron环境调试脚本
 * 用于检查Electron环境下的启动问题
 */

console.log('🔍 Electron环境调试开始...');

// 检查环境变量
console.log('📊 环境信息:');
console.log('- NODE_ENV:', process.env.NODE_ENV);
console.log('- npm_lifecycle_event:', process.env.npm_lifecycle_event);
console.log('- ELECTRON:', process.env.ELECTRON);

// 检查是否在Electron环境
const isElectron = process.env.npm_lifecycle_event?.includes('electron') || 
                  process.env.ELECTRON === 'true';

console.log('🖥️ 运行环境:', isElectron ? 'Electron' : 'Browser');

// 检查缓存相关配置
console.log('💾 缓存配置:');
console.log('- VITE_ENABLE_CACHE_OPTIMIZATION:', process.env.VITE_ENABLE_CACHE_OPTIMIZATION);
console.log('- VITE_CACHE_EXPIRY:', process.env.VITE_CACHE_EXPIRY);
console.log('- VITE_PRELOAD_STRATEGY:', process.env.VITE_PRELOAD_STRATEGY);

// 模拟检查localStorage可用性
try {
  if (typeof localStorage !== 'undefined') {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
    console.log('✅ localStorage 可用');
  } else {
    console.log('❌ localStorage 不可用');
  }
} catch (error) {
  console.log('❌ localStorage 检查失败:', error.message);
}

// 检查window对象
if (typeof window !== 'undefined') {
  console.log('🪟 Window对象可用');
  console.log('- electronAPI:', !!(window as any).electronAPI);
  console.log('- require:', !!window.require);
} else {
  console.log('❌ Window对象不可用');
}

console.log('✅ Electron环境调试完成');
