/**
 * 简化版main.ts - 用于Electron环境调试
 * 最小化导入，避免可能的阻塞问题
 */

console.log("🚀 [Simple Main] 开始启动应用...");

// 检测环境
const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;
console.log("🔍 [Simple Main] 环境检测:", isElectron ? "Electron" : "Browser");

// 基础导入
import { createApp } from "vue";
import App from "./App.vue";

console.log("✅ [Simple Main] Vue和App导入成功");

// 基础样式
import "@/styles/var.scss";
import "@/styles/reset.scss";
import "@/styles/common.scss";
import "element-plus/dist/index.css";

console.log("✅ [Simple Main] 基础样式导入成功");

// 核心依赖
import router from "@/routers";
import pinia from "@/stores";
import I18n from "@/languages/index";

console.log("✅ [Simple Main] 核心依赖导入成功");

// 错误处理
const errorHandler = (error: any, instance: any, info: string) => {
  console.error("❌ [Simple Main] 应用错误:", error, info);
};

// 创建应用
console.log("🔧 [Simple Main] 创建应用实例...");
const app = createApp(App);

// 配置错误处理
app.config.errorHandler = errorHandler;

console.log("🔌 [Simple Main] 注册插件...");

// 注册插件
app
  .use(router)
  .use(pinia)
  .use(I18n);

console.log("✅ [Simple Main] 插件注册成功");

// 挂载应用
console.log("🎯 [Simple Main] 挂载应用...");
app.mount("#app");

console.log("✅ [Simple Main] 应用挂载成功");

// 环境信息
console.group("🔍 [Simple Main] 环境信息");
console.log("- window:", typeof window !== 'undefined');
console.log("- electronAPI:", typeof window !== 'undefined' && !!(window as any).electronAPI);
console.log("- localStorage:", typeof localStorage !== 'undefined');
console.log("- require:", typeof window !== 'undefined' && !!window.require);
console.groupEnd();

console.log("🎉 [Simple Main] 启动完成！");

// 延迟加载非关键资源（仅在浏览器环境）
if (!isElectron) {
  setTimeout(async () => {
    console.log("📦 [Simple Main] 开始延迟加载资源...");
    try {
      // 延迟导入性能监控
      const { markAppMounted, markResourcesLoaded } = await import("@/utils/performance");
      markAppMounted();
      
      // 延迟加载其他样式
      await Promise.all([
        import("@/assets/iconfont/iconfont.scss").catch(console.warn),
        import("@/assets/fonts/font.scss").catch(console.warn)
      ]);
      
      markResourcesLoaded();
      console.log("✅ [Simple Main] 延迟资源加载完成");
    } catch (error) {
      console.warn("❌ [Simple Main] 延迟资源加载失败:", error);
    }
  }, 100);
}
