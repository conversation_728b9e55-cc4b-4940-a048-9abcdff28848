console.log("🚀 [Main] 开始启动应用...");

// 检测Electron环境
const isElectron = typeof window !== "undefined" && (window as any).electronAPI;
console.log("🔍 [Main] 环境检测:", isElectron ? "Electron" : "Browser");

import { createApp } from "vue";
import App from "./App.vue";

console.log("✅ [Main] Vue和App导入成功");

// 性能监控
import { markAppMounted, markResourcesLoaded } from "@/utils/performance";
// 启动缓存
import { warmupStartupCache } from "@/utils/startupCache";
// 启动配置
import { startupConfig, applyStartupOptimizations } from "@/config/startup";

// 关键CSS样式 - 立即加载
import "@/styles/var.scss"; // 首先加载变量定义
import "@/styles/reset.scss";
import "@/styles/common.scss";
// Element Plus 完整样式 - 一次性加载，避免动态导入
import "element-plus/dist/index.css";
import "virtual:uno.css";

// 核心依赖 - 立即加载
// 移除全局 ElementPlus 注册，改用按需引入
// import ElementPlus from "element-plus";
import router from "@/routers";
import pinia from "@/stores";
import errorHandler from "@/utils/errorHandler";
import I18n from "@/languages/index";
// import { routeStateManager } from "@/utils/routeStateManager"; // 已禁用路由状态管理

// 启动测试（仅开发模式且非Electron环境）
if (import.meta.env.DEV && !isElectron) {
  console.log("🧪 [Main] 导入启动测试模块");
  import("@/utils/startupTest").catch(console.warn);
} else if (isElectron) {
  console.log("⚠️ [Main] Electron环境，跳过启动测试");
}

// 创建应用实例
console.log("🔧 [Main] 创建应用实例...");
const app = createApp(App);
console.log("✅ [Main] 应用实例创建成功");

// 应用启动优化配置（仅在浏览器环境）
if (!isElectron && applyStartupOptimizations && startupConfig) {
  console.log("⚙️ [Main] 应用启动优化配置");
  applyStartupOptimizations(startupConfig);
} else {
  console.log("⚠️ [Main] 跳过启动优化配置");
}

// 错误处理
app.config.errorHandler = errorHandler;

// 注册核心插件
console.log("🔌 [Main] 注册核心插件...");
app
  // 移除全局 ElementPlus 注册，改用按需引入
  // .use(ElementPlus, {
  //   size: "default",
  //   zIndex: 3000
  // })
  .use(router)
  .use(pinia)
  .use(I18n);
console.log("✅ [Main] 核心插件注册成功");

// 立即挂载应用，提高启动速度
console.log("🎯 [Main] 挂载应用...");
app.mount("#app");
console.log("✅ [Main] 应用挂载成功");

// 标记应用挂载完成
markAppMounted();

// 预热缓存（异步执行，不阻塞应用挂载）- 仅在浏览器环境
if (!isElectron) {
  console.log("🔥 [Main] 开始预热缓存...");
  warmupStartupCache().catch(console.warn);
} else {
  console.log("⚠️ [Main] Electron环境，跳过缓存预热");
}

// 初始化路由状态管理器 - 已禁用
// routeStateManager.initialize(router);

// 在开发环境下初始化状态调试器
if (import.meta.env.DEV) {
  console.log("🔧 开发模式：状态调试器已启用");
  console.log("💡 使用 stateDebugger.help() 查看可用命令");
}

// 延迟加载非关键资源和功能（Electron环境下减少延迟）
const delayTime = isElectron ? 10 : 50;
console.log(`⏰ [Main] ${delayTime}ms后开始延迟加载资源...`);

setTimeout(async () => {
  try {
    console.log("📦 [Main] 开始延迟加载资源...");
    // 延迟加载样式文件
    await Promise.all([
      import("@/assets/iconfont/iconfont.scss").catch(console.warn),
      import("@/assets/iconfontPlus/iconfont.scss").catch(console.warn),
      import("@/assets/fonts/font.scss").catch(console.warn),
      import("element-plus/theme-chalk/dark/css-vars.css").catch(console.warn),
      import("@/styles/element-dark.scss").catch(console.warn),
      import("@/styles/element.scss").catch(console.warn),
      import("highlight.js/styles/atom-one-dark.css").catch(console.warn),
      import("v-contextmenu/dist/themes/default.css").catch(console.warn)
    ]);

    // 延迟加载和注册组件
    const [
      directivesModule,
      BLRowModule,
      BLColModule,
      iconifyModule,
      IconsModule,
      hljsVuePluginModule,
      contextmenuModule,
      hljsCommonModule,
      echartsModule
    ] = await Promise.all([
      import("@/directives/index").catch(() => ({ default: null })),
      import("@/components/Common/BLRow.vue").catch(() => ({ default: null })),
      import("@/components/Common/BLCol.vue").catch(() => ({ default: null })),
      import("@/utils/iconify").catch(() => ({
        downloadAndInstall: () => console.warn("图标加载失败")
      })),
      import("@element-plus/icons-vue").catch(() => ({})),
      import("@highlightjs/vue-plugin").catch(() => ({ default: null })),
      import("v-contextmenu").catch(() => ({ default: null })),
      import("highlight.js/lib/common").catch(() => ({ default: null })),
      import("echarts/core").catch(() => ({
        use: () => console.warn("ECharts加载失败")
      }))
    ]);

    const directives = directivesModule.default;
    const BLRow = BLRowModule.default;
    const BLCol = BLColModule.default;
    const { downloadAndInstall } = iconifyModule;

    // 注册延迟加载的插件
    if (directives) {
      app.use(directives);
    }
    if (hljsVuePluginModule.default) {
      app.use(hljsVuePluginModule.default);
    }
    if (contextmenuModule.default) {
      app.use(contextmenuModule.default);
    }

    // 注册全局组件
    if (BLRow && BLCol) {
      app.component("BlRow", BLRow).component("BLCol", BLCol);
    }

    // 按需注册常用图标
    const iconList = [
      "Edit",
      "Delete",
      "Search",
      "Refresh",
      "Plus",
      "Minus",
      "ColdDrink",
      "Setting",
      "Notification",
      "CircleCheckFilled",
      "ChromeFilled",
      "CircleClose",
      "FolderDelete",
      "Remove",
      "DArrowLeft",
      "DArrowRight",
      "More"
    ];

    iconList.forEach(key => {
      if (IconsModule[key]) {
        app.component(key, IconsModule[key]);
      }
    });

    // 异步加载图标集合
    downloadAndInstall();

    // 延迟初始化 ECharts
    const { LineChart, BarChart, PieChart } = await import("echarts/charts");
    const { TitleComponent, TooltipComponent, LegendComponent, GridComponent } = await import("echarts/components");
    const { CanvasRenderer } = await import("echarts/renderers");

    echartsModule.use([LineChart, BarChart, PieChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, CanvasRenderer]);

    // 延迟初始化 highlight.js
    try {
      if (hljsCommonModule.default && hljsCommonModule.default.highlightAuto) {
        hljsCommonModule.default.highlightAuto("<h1>Highlight.js has been registered successfully!</h1>").value;
      }
    } catch (error) {
      console.warn("Highlight.js初始化失败:", error);
    }

    // 标记资源加载完成
    markResourcesLoaded();
    console.log("✅ [Main] 延迟资源加载完成");
  } catch (error) {
    console.warn("❌ [Main] 延迟资源加载失败:", error);
    markResourcesLoaded(); // 即使失败也要标记完成
  }
}, delayTime); // Electron环境10ms，浏览器环境50ms

console.log("🎉 [Main] 主启动流程完成！");
