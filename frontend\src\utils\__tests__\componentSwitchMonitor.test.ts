/**
 * ComponentSwitchMonitor 测试用例
 */

import { componentSwitchMonitor } from '../componentSwitchMonitor';

describe('ComponentSwitchMonitor', () => {
  beforeEach(() => {
    // 清空历史记录和当前状态
    componentSwitchMonitor.clearHistory();
    // 确保没有正在进行的切换
    if (componentSwitchMonitor.getCurrentSwitch()) {
      componentSwitchMonitor.endSwitch(true);
    }
  });

  test('应该能够正常开始和结束切换', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    
    componentSwitchMonitor.startSwitch('test-component', false);
    expect(componentSwitchMonitor.getCurrentSwitch()).toBeTruthy();
    
    componentSwitchMonitor.endSwitch();
    expect(componentSwitchMonitor.getCurrentSwitch()).toBeNull();
    
    consoleSpy.mockRestore();
  });

  test('多次调用 endSwitch 不应该产生警告', () => {
    const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();
    const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
    
    // 开始切换
    componentSwitchMonitor.startSwitch('test-component', false);
    
    // 正常结束切换
    componentSwitchMonitor.endSwitch();
    
    // 再次调用 endSwitch 应该产生警告
    componentSwitchMonitor.endSwitch();
    
    expect(consoleWarnSpy).toHaveBeenCalledWith('⚠️ [ComponentSwitch] 没有正在进行的组件切换');
    
    consoleWarnSpy.mockRestore();
    consoleLogSpy.mockRestore();
  });

  test('getCurrentSwitch 应该正确返回当前切换状态', () => {
    expect(componentSwitchMonitor.getCurrentSwitch()).toBeNull();
    
    componentSwitchMonitor.startSwitch('test-component', false);
    const currentSwitch = componentSwitchMonitor.getCurrentSwitch();
    expect(currentSwitch).toBeTruthy();
    expect(currentSwitch?.componentKey).toBe('test-component');
    
    componentSwitchMonitor.endSwitch();
    expect(componentSwitchMonitor.getCurrentSwitch()).toBeNull();
  });

  test('recordError 应该只在有正在进行的切换时调用 endSwitch', () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
    
    // 没有正在进行的切换时调用 recordError
    componentSwitchMonitor.recordError('测试错误');
    // 不应该有任何输出，因为没有正在进行的切换
    expect(consoleErrorSpy).not.toHaveBeenCalled();
    
    // 开始切换后调用 recordError
    componentSwitchMonitor.startSwitch('test-component', false);
    componentSwitchMonitor.recordError('测试错误');
    expect(consoleErrorSpy).toHaveBeenCalledWith('❌ [ComponentSwitch] 组件切换错误: 测试错误');
    
    consoleErrorSpy.mockRestore();
    consoleLogSpy.mockRestore();
  });
});
