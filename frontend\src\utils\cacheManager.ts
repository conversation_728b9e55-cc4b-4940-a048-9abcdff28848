/**
 * 缓存管理工具
 * 提供统一的缓存管理接口，支持多种缓存策略
 */

interface CacheOptions {
  // 缓存过期时间（毫秒）
  expiry?: number;
  // 缓存版本
  version?: string;
  // 是否压缩存储
  compress?: boolean;
  // 缓存优先级
  priority?: "high" | "medium" | "low";
}

interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  expiry?: number;
  version: string;
  priority: string;
  compressed: boolean;
  size: number;
}

interface CacheStats {
  totalItems: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  expiredItems: number;
}

class CacheManager {
  private readonly prefix = "visualdebug_cache_";
  private readonly version = "1.0.0";
  private readonly maxSize = 50 * 1024 * 1024; // 50MB
  private hitCount = 0;
  private missCount = 0;

  /**
   * 设置缓存
   */
  set<T>(key: string, data: T, options: CacheOptions = {}): boolean {
    try {
      // 检查localStorage是否可用
      if (typeof localStorage === "undefined") {
        console.warn(`localStorage不可用，无法设置缓存: ${key}`);
        return false;
      }

      const {
        expiry = 24 * 60 * 60 * 1000, // 默认24小时
        version = this.version,
        compress = false,
        priority = "medium"
      } = options;

      let serializedData = JSON.stringify(data);

      // 简单压缩（实际项目中可以使用更好的压缩算法）
      if (compress && serializedData.length > 1024) {
        // 这里可以集成压缩库，如 lz-string
        serializedData = this.simpleCompress(serializedData);
      }

      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        expiry,
        version,
        priority,
        compressed: compress,
        size: new Blob([serializedData]).size
      };

      // 检查存储空间
      if (!this.checkStorageSpace(cacheItem.size)) {
        this.cleanup();
        if (!this.checkStorageSpace(cacheItem.size)) {
          console.warn(`缓存空间不足，无法存储: ${key}`);
          return false;
        }
      }

      localStorage.setItem(this.prefix + key, JSON.stringify(cacheItem));
      console.log(`✅ 缓存已设置: ${key} (${this.formatSize(cacheItem.size)})`);
      return true;
    } catch (error) {
      console.error(`❌ 设置缓存失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 获取缓存
   */
  get<T>(key: string): T | null {
    try {
      // 检查localStorage是否可用
      if (typeof localStorage === "undefined") {
        this.missCount++;
        return null;
      }

      const cached = localStorage.getItem(this.prefix + key);
      if (!cached) {
        this.missCount++;
        return null;
      }

      const cacheItem: CacheItem<T> = JSON.parse(cached);

      // 检查版本
      if (cacheItem.version !== this.version) {
        console.log(`🔄 缓存版本不匹配，清除: ${key}`);
        this.remove(key);
        this.missCount++;
        return null;
      }

      // 检查过期时间
      if (cacheItem.expiry && Date.now() - cacheItem.timestamp > cacheItem.expiry) {
        console.log(`⏰ 缓存已过期，清除: ${key}`);
        this.remove(key);
        this.missCount++;
        return null;
      }

      this.hitCount++;
      console.log(`✅ 缓存命中: ${key}`);
      return cacheItem.data;
    } catch (error) {
      console.error(`❌ 获取缓存失败: ${key}`, error);
      this.remove(key);
      this.missCount++;
      return null;
    }
  }

  /**
   * 移除缓存
   */
  remove(key: string): void {
    try {
      localStorage.removeItem(this.prefix + key);
      console.log(`🗑️ 缓存已移除: ${key}`);
    } catch (error) {
      console.error(`❌ 移除缓存失败: ${key}`, error);
    }
  }

  /**
   * 清除所有缓存
   */
  clear(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key);
        }
      });
      console.log("🧹 所有缓存已清除");
    } catch (error) {
      console.error("❌ 清除缓存失败", error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    let totalItems = 0;
    let totalSize = 0;
    let expiredItems = 0;

    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          totalItems++;
          const cached = localStorage.getItem(key);
          if (cached) {
            try {
              const cacheItem: CacheItem = JSON.parse(cached);
              totalSize += cacheItem.size;

              if (cacheItem.expiry && Date.now() - cacheItem.timestamp > cacheItem.expiry) {
                expiredItems++;
              }
            } catch (e) {
              expiredItems++;
            }
          }
        }
      });
    } catch (error) {
      console.error("❌ 获取缓存统计失败", error);
    }

    const totalRequests = this.hitCount + this.missCount;
    const hitRate = totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0;
    const missRate = totalRequests > 0 ? (this.missCount / totalRequests) * 100 : 0;

    return {
      totalItems,
      totalSize,
      hitRate: Math.round(hitRate * 100) / 100,
      missRate: Math.round(missRate * 100) / 100,
      expiredItems
    };
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    console.log("🧹 开始清理过期缓存...");
    let cleanedCount = 0;
    let freedSize = 0;

    try {
      const keys = Object.keys(localStorage);
      const cacheKeys = keys.filter(key => key.startsWith(this.prefix));

      // 按优先级和时间排序，优先清理低优先级和过期的缓存
      const cacheItems = cacheKeys
        .map(key => {
          const cached = localStorage.getItem(key);
          if (cached) {
            try {
              const cacheItem: CacheItem = JSON.parse(cached);
              return { key, cacheItem };
            } catch (e) {
              return { key, cacheItem: null };
            }
          }
          return { key, cacheItem: null };
        })
        .filter(item => item.cacheItem !== null);

      // 排序：过期的 > 低优先级 > 旧的
      cacheItems.sort((a, b) => {
        const aExpired = a.cacheItem!.expiry && Date.now() - a.cacheItem!.timestamp > a.cacheItem!.expiry;
        const bExpired = b.cacheItem!.expiry && Date.now() - b.cacheItem!.timestamp > b.cacheItem!.expiry;

        if (aExpired && !bExpired) return -1;
        if (!aExpired && bExpired) return 1;

        const priorityOrder = { low: 0, medium: 1, high: 2 };
        const aPriority = priorityOrder[a.cacheItem!.priority as keyof typeof priorityOrder] || 1;
        const bPriority = priorityOrder[b.cacheItem!.priority as keyof typeof priorityOrder] || 1;

        if (aPriority !== bPriority) return aPriority - bPriority;

        return a.cacheItem!.timestamp - b.cacheItem!.timestamp;
      });

      // 清理缓存直到空间足够
      for (const { key, cacheItem } of cacheItems) {
        if (this.getCurrentStorageSize() < this.maxSize * 0.8) break; // 保持80%以下使用率

        freedSize += cacheItem!.size;
        localStorage.removeItem(key);
        cleanedCount++;
      }
    } catch (error) {
      console.error("❌ 清理缓存失败", error);
    }

    console.log(`🧹 清理完成: 清理了 ${cleanedCount} 个缓存项，释放 ${this.formatSize(freedSize)} 空间`);
  }

  /**
   * 检查存储空间
   */
  private checkStorageSpace(requiredSize: number): boolean {
    const currentSize = this.getCurrentStorageSize();
    return currentSize + requiredSize <= this.maxSize;
  }

  /**
   * 获取当前存储大小
   */
  private getCurrentStorageSize(): number {
    let totalSize = 0;
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          const value = localStorage.getItem(key);
          if (value) {
            totalSize += new Blob([value]).size;
          }
        }
      });
    } catch (error) {
      console.error("❌ 计算存储大小失败", error);
    }
    return totalSize;
  }

  /**
   * 简单压缩（示例实现）
   */
  private simpleCompress(str: string): string {
    // 这里可以集成真正的压缩算法
    return str;
  }

  /**
   * 格式化文件大小
   */
  private formatSize(bytes: number): string {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * 打印缓存报告
   */
  printReport(): void {
    const stats = this.getStats();
    console.group("📊 缓存管理报告");
    console.log(`总缓存项: ${stats.totalItems}`);
    console.log(`总大小: ${this.formatSize(stats.totalSize)}`);
    console.log(`命中率: ${stats.hitRate}%`);
    console.log(`未命中率: ${stats.missRate}%`);
    console.log(`过期项: ${stats.expiredItems}`);
    console.log(`存储使用率: ${((stats.totalSize / this.maxSize) * 100).toFixed(2)}%`);
    console.groupEnd();
  }
}

// 创建全局缓存管理器实例
export const cacheManager = new CacheManager();

// 在开发环境下暴露到全局（仅在浏览器环境）
if (import.meta.env.DEV && typeof window !== "undefined") {
  (window as any).cacheManager = cacheManager;
  console.log("🔧 缓存管理器已暴露到全局: window.cacheManager");
}
