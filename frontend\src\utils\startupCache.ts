/**
 * 启动缓存管理器
 * 用于缓存启动时需要的配置、菜单等数据，提高启动速度
 */

import { cacheManager } from "./cacheManager";

interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  version: string;
  expiry?: number; // 过期时间（毫秒）
}

interface StartupCacheData {
  menuList?: any[];
  userInfo?: any;
  systemConfig?: any;
  authButtons?: string[];
  themeConfig?: any;
  i18nData?: any;
}

class StartupCacheManager {
  private readonly CACHE_PREFIX = "visualdebug_startup_";
  private readonly DEFAULT_EXPIRY = 24 * 60 * 60 * 1000; // 24小时
  private readonly CACHE_VERSION = "1.0.0";

  /**
   * 设置缓存项
   */
  setCache<T>(key: keyof StartupCacheData, data: T, expiry?: number): void {
    try {
      // 使用新的缓存管理器
      const success = cacheManager.set(`startup_${key}`, data, {
        expiry: expiry || this.DEFAULT_EXPIRY,
        version: this.CACHE_VERSION,
        priority: "high", // 启动缓存优先级高
        compress: true // 启用压缩
      });

      if (success) {
        console.log(`✅ 启动缓存已保存: ${key}`);
      } else {
        console.warn(`❌ 启动缓存保存失败: ${key}`);
      }
    } catch (error) {
      console.warn(`❌ 启动缓存保存失败: ${key}`, error);
    }
  }

  /**
   * 获取缓存项
   */
  getCache<T>(key: keyof StartupCacheData): T | null {
    try {
      // 使用新的缓存管理器
      const cached = cacheManager.get<T>(`startup_${key}`);

      if (cached !== null) {
        console.log(`✅ 启动缓存命中: ${key}`);
        return cached;
      } else {
        console.log(`🔍 启动缓存未命中: ${key}`);
        return null;
      }
    } catch (error) {
      console.warn(`❌ 启动缓存读取失败: ${key}`, error);
      return null;
    }
  }

  /**
   * 移除缓存项
   */
  removeCache(key: keyof StartupCacheData): void {
    try {
      localStorage.removeItem(this.CACHE_PREFIX + key);
    } catch (error) {
      console.warn(`❌ 缓存删除失败: ${key}`, error);
    }
  }

  /**
   * 清除所有启动缓存
   */
  clearAllCache(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.CACHE_PREFIX)) {
          localStorage.removeItem(key);
        }
      });
      console.log("🧹 所有启动缓存已清除");
    } catch (error) {
      console.warn("❌ 清除缓存失败", error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { [key: string]: any } {
    const stats: { [key: string]: any } = {};

    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.CACHE_PREFIX)) {
          const cacheKey = key.replace(this.CACHE_PREFIX, "");
          const cached = localStorage.getItem(key);

          if (cached) {
            try {
              const cacheItem: CacheItem = JSON.parse(cached);
              const age = Date.now() - cacheItem.timestamp;
              const isExpired = cacheItem.expiry ? age > cacheItem.expiry : false;

              stats[cacheKey] = {
                size: new Blob([cached]).size,
                age: Math.round(age / 1000), // 秒
                expired: isExpired,
                version: cacheItem.version
              };
            } catch (e) {
              stats[cacheKey] = { error: "Invalid cache data" };
            }
          }
        }
      });
    } catch (error) {
      console.warn("❌ 获取缓存统计失败", error);
    }

    return stats;
  }

  /**
   * 预热缓存 - 在应用启动时调用
   */
  async warmupCache(): Promise<void> {
    console.log("🔥 开始预热启动缓存...");

    const startTime = performance.now();

    // 检查并预加载关键缓存
    const criticalCaches: (keyof StartupCacheData)[] = ["menuList", "userInfo", "systemConfig"];

    const cachePromises = criticalCaches.map(async key => {
      const cached = this.getCache(key);
      if (cached) {
        console.log(`📦 预热缓存: ${key}`);
        return { key, cached: true };
      } else {
        console.log(`🔍 缓存未命中: ${key}`);
        return { key, cached: false };
      }
    });

    const results = await Promise.all(cachePromises);
    const duration = performance.now() - startTime;

    console.log(`🔥 缓存预热完成，耗时: ${duration.toFixed(2)}ms`);
    console.log("📊 缓存状态:", results);
  }

  /**
   * 批量设置缓存
   */
  setBatchCache(data: Partial<StartupCacheData>): void {
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        this.setCache(key as keyof StartupCacheData, value);
      }
    });
  }

  /**
   * 批量获取缓存
   */
  getBatchCache(keys: (keyof StartupCacheData)[]): Partial<StartupCacheData> {
    const result: Partial<StartupCacheData> = {};

    keys.forEach(key => {
      const cached = this.getCache(key);
      if (cached !== null) {
        result[key] = cached;
      }
    });

    return result;
  }

  /**
   * 检查缓存健康状态
   */
  checkCacheHealth(): { healthy: boolean; issues: string[] } {
    const issues: string[] = [];

    try {
      // 检查localStorage可用性
      const testKey = this.CACHE_PREFIX + "test";
      localStorage.setItem(testKey, "test");
      localStorage.removeItem(testKey);
    } catch (error) {
      issues.push("localStorage不可用");
    }

    // 检查缓存大小
    try {
      const usage = this.getCacheUsage();
      if (usage > 5 * 1024 * 1024) {
        // 5MB
        issues.push("缓存占用过大");
      }
    } catch (error) {
      issues.push("无法检查缓存大小");
    }

    return {
      healthy: issues.length === 0,
      issues
    };
  }

  /**
   * 获取缓存使用量（字节）
   */
  private getCacheUsage(): number {
    let totalSize = 0;

    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.CACHE_PREFIX)) {
          const value = localStorage.getItem(key);
          if (value) {
            totalSize += new Blob([value]).size;
          }
        }
      });
    } catch (error) {
      console.warn("❌ 计算缓存大小失败", error);
    }

    return totalSize;
  }
}

// 创建全局缓存管理器实例
export const startupCache = new StartupCacheManager();

// 导出便捷方法
export const setStartupCache = <T>(key: keyof StartupCacheData, data: T, expiry?: number) => startupCache.setCache(key, data, expiry);

export const getStartupCache = <T>(key: keyof StartupCacheData): T | null => startupCache.getCache<T>(key);

export const clearStartupCache = () => startupCache.clearAllCache();

export const warmupStartupCache = () => startupCache.warmupCache();
