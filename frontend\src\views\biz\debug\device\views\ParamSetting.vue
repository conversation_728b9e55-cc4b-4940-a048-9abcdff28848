<template>
  <div class="table-box">
    <!-- 差异对比弹窗 -->
    <ParamCompareDialog
      v-if="showDiffDialog"
      v-model:visible="showDiffDialog"
      :diffdata="diffdata"
      @confirm="confirmImport"
      @cancel="showDiffDialog = false"
    />

    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getParamList"
      :init-param="initParam"
      highlight-current-row
      :request-auto="false"
      :data-callback="dataCallback"
      row-key="paramName"
      :cell-style="cellStyle"
      table-key="paramSetting"
    >
      <template #operation="scope">
        <el-button type="primary" link :icon="EditPen" @click="confirmSelect(scope.row)">{{ t("device.paramSetting.confirm") }}</el-button>
      </template>
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <div class="flex flex-wrap gap-4 items-center header">
          <el-checkbox v-model="refreshCheck" :label="t('device.paramSetting.autoRefresh')" size="large" />
          <template v-if="debugIndex.compData.get(currDevice.id).fc === 'SG'">
            <div class="flex items-center gap-2">
              <span class="text-sm">{{ t("device.paramSetting.currentEditArea") }}:</span>
              <el-input-number v-model="currentEditArea" :min="1" :max="maxEditArea" :controls="false" size="small" style="width: 80px" readonly />
              <span class="text-sm">{{ t("device.paramSetting.selectEditArea") }}:</span>
              <el-input-number v-model="selectedEditArea" :min="1" :max="maxEditArea" :controls="false" size="small" style="width: 80px" readonly />
              <el-button type="primary" :icon="EditPen" @click="setEditArea">{{ t("device.paramSetting.setEditArea") }}</el-button>
            </div>
          </template>
          <el-button type="primary" plain :icon="Refresh" @click="refreshParam">{{ t("device.paramSetting.refresh") }}</el-button>
          <el-button type="primary" :icon="EditPen" @click="confirmParam">{{ t("device.paramSetting.confirm") }}</el-button>
          <el-button type="success" :icon="Upload" @click="showCompare">{{ t("device.paramSetting.import") }}</el-button>
          <el-button type="success" :icon="Download" @click="exportParam">{{ t("device.paramSetting.export") }}</el-button>
        </div>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
  <template v-if="setEditAreaDialogVisible">
    <el-dialog v-model="setEditAreaDialogVisible" :title="t('device.paramSetting.setEditAreaTitle')" width="350px" class="set-edit-area-dialog">
      <div class="set-edit-area-content">
        <el-input-number v-model="setEditAreaInput" :min="1" :max="maxEditArea" style="width: 120px" size="large" />
      </div>
      <template #footer>
        <el-button @click="setEditAreaDialogVisible = false">{{ t("common.cancel") }}</el-button>
        <el-button type="primary" @click="handleSetEditAreaConfirm">{{ t("common.confirm") }}</el-button>
      </template>
    </el-dialog>
  </template>
</template>

<script setup lang="tsx" name="useProTa1ble">
import { ref, reactive, watch } from "vue";
import { useI18n } from "vue-i18n";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Upload, EditPen, Download, Refresh } from "@element-plus/icons-vue";
// const router = useRouter();
import { paramSettingApi } from "@/api/modules/biz/debug/paramsetting";
import { osControlApi } from "@/api/modules/biz/os";
import { useDebugStore } from "@/stores/modules/debug";
import ParamCompareDialog from "../dialog/ParamCompareDialog.vue"; // 引入差异弹窗组件
import { IpUtils } from "@/utils/iec/ipUtils";
import { DiffItem } from "@/api/interface/biz/debug/diffitem";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import Decimal from "decimal.js";
const { debugIndex, currDevice } = useDebugStore();
import { useConfigStore } from "@/stores/modules";
const { paramInfo } = useConfigStore();
const progressDialog = ref();

const { addConsole } = useDebugStore();
// ProTable 实例
const proTable = ref<ProTableInstance>();

const refreshCheck = ref(false);
const currentEditArea = ref(1);

// 添加缺失的变量和函数
const selectedEditArea = ref(1);
// 新增最大定值区变量
const maxEditArea = ref(100);

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段

const showDiffDialog = ref(false);

// 差异数据
const diffdata = ref<DiffItem[]>([]); // 明确指定数组类型

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

const { t } = useI18n();

// 确认导入
const confirmImport = async data => {
  console.log("confirmParam");
  showDiffDialog.value = false;
  // 获取所有被修改的数据
  const modifiedData = data;

  // 如果没有修改的数据，提示用户
  if (!modifiedData || modifiedData.length === 0) {
    ElMessageBox.alert(t("device.paramSetting.noDataToImport"), t("common.warning"), {
      confirmButtonText: t("common.confirm"),
      type: "warning"
    });
    return;
  }

  const updateItems = modifiedData.map(item => ({
    grp: item.grp,
    inf: item.inf,
    name: item.name,
    v: item.newValue
  }));

  try {
    // 提交数据到 API
    const response = await paramSettingApi.confirmParam(updateItems);
    console.log("response:", response);
    // 处理返回结果
    if (Number(response.code) === 0) {
      addConsole(t("device.paramSetting.importSuccess"));
      ElMessageBox.alert(t("device.paramSetting.importSuccess"), t("common.success"), {
        confirmButtonText: t("common.confirm"),
        type: "success"
      }).then(() => {
        proTable.value?.getTableList();
      });
    } else {
      addConsole(t("device.paramSetting.importFailed") + ": " + response.msg);
      ElMessageBox.alert(t("device.paramSetting.importFailed") + ": " + response.msg, t("common.error"), {
        confirmButtonText: t("common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("common.requestFailed"), error);
    ElMessageBox.alert(t("device.paramSetting.requestFailed"), t("common.error"), {
      confirmButtonText: t("common.confirm"),
      type: "error"
    });
  }
};

// 界面加载时，查询当前运行定值区，设置输入框默认值，执行查询
onMounted(async () => {
  if (debugIndex.compData.get(currDevice.id).fc === "SG") {
    // 查询当前运行定值区
    try {
      const response = await paramSettingApi.getCurrentRunArea();
      if (response.code === 0 && response.data) {
        // 设置输入框默认值
        console.log("获取当前运行定值区响应:", response);
        console.log(response.data && "actSG" in response.data);
        console.log(response.data && "editSG" in response.data);
        // 新增：设置最大定值区
        if (response.data && "numOfSG" in response.data) {
          maxEditArea.value = Number(response.data.numOfSG);
        }
        if (response.data && "actSG" in response.data) {
          currentEditArea.value = Number(response.data.actSG);
        }
        if (response.data && "editSG" in response.data) {
          selectedEditArea.value = Number(response.data.editSG);
          setEditAreaInput.value = Number(response.data.editSG);
          // 如果当前编辑区为0 则设置为1
          console.log(selectedEditArea.value);
          if (selectedEditArea.value === 0) {
            selectedEditArea.value = 1;
            setEditAreaInput.value = 1;
          }
        }
        proTable.value?.getTableList();
      } else {
        ElMessage.warning(t("device.paramSetting.getCurrentRunAreaFailed"));
      }
    } catch (error) {
      console.error("获取定值区失败:", error);
      ElMessage.error(t("device.paramSetting.getCurrentRunAreaFailed"));
    }
  } else {
    // 执行查询
    proTable.value?.getTableList();
  }
});

// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = async (data: any): Promise<{ list: any[]; total: number }> => {
  try {
    // hideLoading();
    if (!data || !Array.isArray(data.list)) {
      console.warn("Invalid data received:", data);
      return { list: [], total: 0 };
    }
    console.log("dataCallback", data.list);

    // 直接在原数据上添加 isModified 和 originalValue 属性
    data.list.forEach(item => {
      item.isModified = false;
      item.originalValue = item.value; // 保存原始值
    });

    // 检查是否有 isModified 为 true 的数据
    const hasModified = data.list.some(item => item.isModified);
    if (hasModified) {
      refreshCheck.value = false; // 禁止自动刷新
    }

    return {
      list: data.list,
      total: data.total
    };
  } catch (error) {
    console.error("Error in dataCallback:", error);
    throw error;
  }
};

const showLoading = () => {
  progressDialog.value.show();
};
const hideLoading = () => {
  progressDialog.value.hide();
};
// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getParamList = async (params: any) => {
  try {
    await setEditAreaConfirm();
  } catch {
    return { list: [], total: 0 };
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  newParams.grpName = debugIndex.compData.get(currDevice.id).name;
  // newParams.runArea = selectedEditArea.value;
  // await paramSettingApi.selectRunArea({ runArea: selectedEditArea.value });
  delete newParams.createTime;
  try {
    const result = await paramSettingApi.getParam(newParams);
    if (Number(result.code) !== 0) {
      addConsole(t("device.paramSetting.queryFailed", { msg: result.msg }));
      ElMessageBox.alert(t("device.paramSetting.queryFailed", { msg: result.msg }), t("common.error"), {
        confirmButtonText: t("common.confirm"),
        type: "error"
      });
    }
    return result || { list: [], total: 0 };
  } catch (error) {
    console.error(t("common.requestFailed"), error);
    return { list: [], total: 0 };
  }
};

// 页面按钮权限（按钮权限既可以使用 hooks，也可以直接使用 v-auth 指令，指令适合直接绑定在按钮上，hooks 适合根据按钮权限显示不同的内容）
// const { BUTTONS } = useAuthButtons();

const refreshParam = async () => {
  if (proTable.value?.tableData.some(row => row.isModified)) {
    ElMessageBox.confirm(t("device.paramSetting.modifiedWarning"), t("common.warning"), {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning"
    })
      .then(() => {
        proTable.value?.getTableList();
      })
      .catch(() => {
        // 用户取消刷新，不需要执行任何操作
        return;
      });
  } else {
    proTable.value?.getTableList();
  }
};

const isValid = (num: any, dataType: string, min: any, max: any, step: number) => {
  if (dataType === "s") {
    return true;
  }
  let realValue = num.trim();
  if (realValue.length == 0) {
    return false;
  }
  if (dataType === "net") {
    if (!IpUtils.validateIp(realValue)) {
      return false;
    }
    realValue = IpUtils.ipToNumber(realValue);
    console.log(IpUtils.ipToNumber(max));
    min = IpUtils.ipToNumber(min);
    max = IpUtils.ipToNumber(max);
  }
  min = Number(min);
  max = Number(max);
  const value = Number(realValue);
  // 检查是否为有效数值
  if (typeof value !== "number" || isNaN(value)) {
    return false;
  }
  // 处理步长为0的情况
  if (step === 0) {
    return false;
  }
  const diff = new Decimal(value).minus(min);
  const n = diff.dividedBy(step);
  return n.isInteger() && value >= min && value <= max;
};

const confirmParam = async () => {
  const modifiedData = proTable.value?.tableData.filter(row => row.isModified);

  if (!modifiedData || modifiedData.length === 0) {
    ElMessageBox.alert(t("device.paramSetting.noDataToConfirm"), t("common.warning"), {
      confirmButtonText: t("common.confirm"),
      type: "warning"
    });
    return;
  }

  const updateItems = modifiedData.map(item => ({
    grp: item.grp,
    inf: item.inf,
    name: item.paramName,
    v: item.value
  }));
  showLoading();
  try {
    try {
      await setEditAreaConfirm();
    } catch {
      return;
    }
    const response = await paramSettingApi.confirmParam(updateItems);
    if (Number(response.code) === 0) {
      addConsole(t("device.paramSetting.updateSuccess"));
      ElMessageBox.alert(t("device.paramSetting.updateSuccess"), t("common.success"), {
        confirmButtonText: t("common.confirm"),
        type: "success"
      }).then(() => {
        proTable.value?.getTableList();
      });
    } else {
      addConsole(t("device.paramSetting.updateFailed") + ": " + response.msg);
      ElMessageBox.alert(t("device.paramSetting.updateFailed") + ": " + response.msg, t("common.error"), {
        confirmButtonText: t("common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("common.requestFailed"), error);
    ElMessageBox.alert(t("device.paramSetting.requestFailed"), t("common.error"), {
      confirmButtonText: t("common.confirm"),
      type: "error"
    });
  } finally {
    hideLoading();
  }
};
const confirmSelect = async (row: any) => {
  const modifiedData = row.isModified ? [row] : [];

  if (!modifiedData || modifiedData.length === 0) {
    ElMessageBox.alert(t("device.paramSetting.noDataToConfirm"), t("common.warning"), {
      confirmButtonText: t("common.confirm"),
      type: "warning"
    });
    return;
  }
  showLoading();
  const updateItems = modifiedData.map(item => ({
    grp: item.grp,
    inf: item.inf,
    name: item.paramName,
    v: item.value
  }));

  try {
    try {
      await setEditAreaConfirm();
    } catch {
      return;
    }
    const response = await paramSettingApi.confirmParam(updateItems);
    if (Number(response.code) === 0) {
      addConsole(t("device.paramSetting.updateSuccess"));
      ElMessageBox.alert(t("device.paramSetting.updateSuccess"), t("common.success"), {
        confirmButtonText: t("common.confirm"),
        type: "success"
      }).then(() => {
        modifiedData.forEach(row => {
          row.isModified = false;
        });
      });
    } else {
      addConsole(t("device.paramSetting.updateFailed") + ": " + response.msg);
      ElMessageBox.alert(t("device.paramSetting.updateFailed") + ": " + response.msg, t("common.error"), {
        confirmButtonText: t("common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("common.requestFailed"), error);
    ElMessageBox.alert(t("device.paramSetting.requestFailed"), t("common.error"), {
      confirmButtonText: t("common.confirm"),
      type: "error"
    });
  } finally {
    hideLoading();
  }
};
const showCompare = async () => {
  const selectPath = await osControlApi.selectFileByParams({
    title: t("device.paramSetting.title"),
    filterList: [
      { name: "xlsx", extensions: ["xlsx"] },
      { name: "csv", extensions: ["csv"] },
      { name: "xml", extensions: ["xml"] }
    ]
  });
  if (!selectPath.path) {
    return;
  }

  showLoading();
  const path = String(selectPath.path);

  try {
    try {
      await setEditAreaConfirm();
    } catch {
      return;
    }
    const response = await paramSettingApi.getDiffParam({ path, grpName: debugIndex.compData.get(currDevice.id).name });
    if (Number(response.code) === 0 && response.data) {
      diffdata.value = response.data as DiffItem[];
      if (diffdata.value.length == 0) {
        ElMessageBox.alert(t("device.paramSetting.noDiffData"), t("common.warning"), {
          type: "warning"
        });
      } else {
        showDiffDialog.value = true;
      }
    } else {
      ElMessageBox.alert(response.msg, t("common.error"), {
        type: "error"
      });
    }
    addConsole(response.msg);
  } finally {
    hideLoading();
  }
};

const exportParam = async () => {
  const defaultPath = t("device.paramSetting.title") + "_" + debugIndex.compData.get(currDevice.id).label + ".xlsx";
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("device.paramSetting.title"),
    defaultPath,
    filterList: [
      { name: "xlsx", extensions: ["xlsx"] },
      { name: "csv", extensions: ["csv"] },
      { name: "xml", extensions: ["xml"] }
    ]
  });
  if (!selectPath) {
    return;
  }
  const path = String(selectPath);

  showLoading();

  try {
    try {
      await setEditAreaConfirm();
    } catch {
      return;
    }
    const result = await paramSettingApi.exportParam({
      path,
      grpName: debugIndex.compData.get(currDevice.id).name
    });
    if (Number(result.code) === 0) {
      addConsole(t("device.paramSetting.exportSuccess") + "，" + path);
      ElMessageBox.alert(t("device.paramSetting.exportSuccess"), t("common.success"), {
        confirmButtonText: t("common.confirm"),
        type: "success"
      });
    } else {
      addConsole(t("device.paramSetting.exportFailed"));
      ElMessageBox.alert(t("device.paramSetting.exportFailed"), t("common.error"), {
        confirmButtonText: t("common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.paramSetting.exportFailed") + ":", error);
    ElMessageBox.alert(t("device.paramSetting.exportFailed"), t("common.error"), {
      confirmButtonText: t("common.confirm"),
      type: "error"
    });
  } finally {
    hideLoading();
  }
};

// 表格配置项
const columns = reactive<ColumnProps[]>([
  { prop: "index", label: t("device.paramSetting.table.index"), fixed: "left", width: 70 },
  {
    prop: "paramName",
    label: t("device.paramSetting.table.name"),
    width: 260,
    search: {
      el: "input",
      tooltip: t("device.paramSetting.search.namePlaceholder"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "paramDesc",
    label: t("device.paramSetting.table.description"),
    search: {
      el: "input",
      tooltip: t("device.paramSetting.search.descPlaceholder"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "value",
    label: t("device.paramSetting.table.value"),
    width: 180,
    render: scope => {
      const handleChange = async (value: string, row: any) => {
        if (refreshCheck.value) {
          addConsole(t("device.paramSetting.autoRefreshDisabled"));
          ElMessageBox.alert(t("device.paramSetting.autoRefreshDisabled"), t("common.warning"), {
            confirmButtonText: t("common.confirm"),
            type: "warning"
          });
          await nextTick(() => {
            row.value = row.originalValue;
          });
          return;
        }
        if (!isValid(value, row.type, row.minValue, row.maxValue, Number(row.step))) {
          addConsole(t("device.paramSetting.invalidValue", { name: row.paramName, value }));
          ElMessageBox.alert(t("device.paramSetting.invalidValue", { name: row.paramName, value }), t("common.error"), {
            confirmButtonText: t("common.confirm"),
            type: "error"
          });
          await nextTick(() => {
            row.value = row.originalValue;
          });
          return;
        }
        row.isModified = true;
        row.originalValue = value;
      };
      return (
        <div>
          <el-input v-model={scope.row.value} onChange={(value: string) => handleChange(value, scope.row)} />
        </div>
      );
    }
  },
  {
    prop: "minValue",
    label: t("device.paramSetting.table.minValue"),
    width: 100
  },
  {
    prop: "maxValue",
    label: t("device.paramSetting.table.maxValue"),
    width: 100
  },
  {
    prop: "step",
    label: t("device.paramSetting.table.step"),
    width: 100
  },
  {
    prop: "inf",
    width: 80,
    isShow: false,
    label: t("device.paramSetting.table.address")
  },
  {
    prop: "unit",
    label: t("device.paramSetting.table.unit"),
    width: 80
  },
  { prop: "operation", label: t("device.paramSetting.table.operation"), fixed: "right", width: 120 }
]);
const cellStyle = ({ row }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
  if (row.isModified) {
    return { backgroundColor: "var(--el-color-warning-light-9)" }; // 修改时的背景色
  }
  return {};
};

// 定时器相关逻辑
let refreshTimer: NodeJS.Timeout | null = null;
const startRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  refreshTimer = setInterval(() => {
    proTable.value?.getTableList();
  }, paramInfo.PARAM_REFRESH_TIME); // 每 5 秒刷新一次
};
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};
onBeforeUnmount(() => {
  stopRefreshTimer();
});
watch(refreshCheck, newValue => {
  if (newValue) {
    if (proTable.value?.tableData.some(row => row.isModified)) {
      ElMessageBox.confirm(t("device.paramSetting.autoRefreshWarning"), t("common.warning"), {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning"
      })
        .then(() => {
          startRefreshTimer();
        })
        .catch(() => {
          refreshCheck.value = false;
        });
    } else {
      startRefreshTimer();
    }
  } else {
    stopRefreshTimer();
  }
});
watch(
  debugIndex.compData,
  newValue => {
    console.log("newValue:", newValue);
    if (newValue) {
      console.log("grpName", newValue);
      proTable.value?.reset();
      // setTimeout(() => {
      //   proTable.value?.getTableList();
      // }, 5000);
    }
  },
  { deep: true }
);

const setEditAreaDialogVisible = ref(false);
const setEditAreaInput = ref(selectedEditArea.value);

const setEditArea = () => {
  setEditAreaInput.value = selectedEditArea.value;
  setEditAreaDialogVisible.value = true;
};

const setEditAreaConfirm = () => {
  if (debugIndex.compData.get(currDevice.id).fc !== "SG") {
    return;
  }
  return new Promise((resolve, reject) => {
    // 只有在需要切换定值区时才调用API，否则直接resolve
    selectedEditArea.value = setEditAreaInput.value;
    setEditAreaDialogVisible.value = false;
    paramSettingApi
      .selectRunArea({
        runArea: selectedEditArea.value
      })
      .then(success => {
        if (success) {
          // ElMessage.success(t("device.paramSetting.setEditAreaSuccess"));
          selectedEditArea.value = setEditAreaInput.value;
          resolve(true);
        } else {
          ElMessage.error(t("device.paramSetting.setEditAreaFailed"));
          reject();
        }
      })
      .catch(error => {
        console.error("设置定值区失败:", error);
        ElMessage.error(t("device.paramSetting.setEditAreaFailed"));
        reject(error);
      });
  });
};

const handleSetEditAreaConfirm = () => {
  setEditAreaDialogVisible.value = false;
  setEditAreaConfirm();
  proTable.value?.getTableList();
};
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 5px;
}
.text-sm {
  display: inline-block;
  white-space: nowrap;
}
.set-edit-area-dialog :deep(.el-dialog__body) {
  padding: 24px 16px 8px;
}
.set-edit-area-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}
</style>
