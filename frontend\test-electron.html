<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Electron 环境测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Electron 环境测试</h1>
        
        <div id="environment-info">
            <h2>环境信息</h2>
            <div id="env-results"></div>
        </div>
        
        <div id="storage-test">
            <h2>存储测试</h2>
            <div id="storage-results"></div>
        </div>
        
        <div id="module-test">
            <h2>模块加载测试</h2>
            <div id="module-results"></div>
            <button onclick="testModuleLoading()">测试模块加载</button>
        </div>
        
        <div id="console-output">
            <h2>控制台输出</h2>
            <pre id="console-log"></pre>
            <button onclick="clearConsole()">清空日志</button>
        </div>
    </div>

    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        const logElement = document.getElementById('console-log');
        
        function addToLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            logElement.textContent += `[${timestamp}] ${type}: ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        console.log = (...args) => {
            originalLog(...args);
            addToLog('LOG', ...args);
        };
        
        console.warn = (...args) => {
            originalWarn(...args);
            addToLog('WARN', ...args);
        };
        
        console.error = (...args) => {
            originalError(...args);
            addToLog('ERROR', ...args);
        };
        
        function clearConsole() {
            logElement.textContent = '';
        }
        
        // 环境检测
        function checkEnvironment() {
            const envResults = document.getElementById('env-results');
            const results = [];
            
            // 基础环境检测
            results.push({
                name: 'Window对象',
                status: typeof window !== 'undefined' ? 'success' : 'error',
                value: typeof window !== 'undefined' ? '可用' : '不可用'
            });
            
            results.push({
                name: 'Electron API',
                status: typeof window !== 'undefined' && window.electronAPI ? 'success' : 'warning',
                value: typeof window !== 'undefined' && window.electronAPI ? '可用' : '不可用'
            });
            
            results.push({
                name: 'Node require',
                status: typeof window !== 'undefined' && window.require ? 'success' : 'warning',
                value: typeof window !== 'undefined' && window.require ? '可用' : '不可用'
            });
            
            results.push({
                name: 'User Agent',
                status: 'info',
                value: navigator.userAgent
            });
            
            envResults.innerHTML = results.map(result => 
                `<div class="status ${result.status}">
                    <strong>${result.name}:</strong> ${result.value}
                </div>`
            ).join('');
        }
        
        // 存储测试
        function checkStorage() {
            const storageResults = document.getElementById('storage-results');
            const results = [];
            
            // localStorage测试
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                results.push({
                    name: 'localStorage',
                    status: 'success',
                    value: '可用'
                });
            } catch (error) {
                results.push({
                    name: 'localStorage',
                    status: 'error',
                    value: `不可用: ${error.message}`
                });
            }
            
            // sessionStorage测试
            try {
                sessionStorage.setItem('test', 'test');
                sessionStorage.removeItem('test');
                results.push({
                    name: 'sessionStorage',
                    status: 'success',
                    value: '可用'
                });
            } catch (error) {
                results.push({
                    name: 'sessionStorage',
                    status: 'error',
                    value: `不可用: ${error.message}`
                });
            }
            
            storageResults.innerHTML = results.map(result => 
                `<div class="status ${result.status}">
                    <strong>${result.name}:</strong> ${result.value}
                </div>`
            ).join('');
        }
        
        // 模块加载测试
        async function testModuleLoading() {
            const moduleResults = document.getElementById('module-results');
            moduleResults.innerHTML = '<div class="status info">正在测试模块加载...</div>';
            
            const tests = [
                {
                    name: 'Vue',
                    test: () => import('vue')
                },
                {
                    name: 'Element Plus',
                    test: () => import('element-plus')
                }
            ];
            
            const results = [];
            
            for (const test of tests) {
                try {
                    console.log(`测试加载模块: ${test.name}`);
                    await test.test();
                    results.push({
                        name: test.name,
                        status: 'success',
                        value: '加载成功'
                    });
                    console.log(`✅ ${test.name} 加载成功`);
                } catch (error) {
                    results.push({
                        name: test.name,
                        status: 'error',
                        value: `加载失败: ${error.message}`
                    });
                    console.error(`❌ ${test.name} 加载失败:`, error);
                }
            }
            
            moduleResults.innerHTML = results.map(result => 
                `<div class="status ${result.status}">
                    <strong>${result.name}:</strong> ${result.value}
                </div>`
            ).join('');
        }
        
        // 页面加载完成后执行检测
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 页面加载完成，开始环境检测');
            checkEnvironment();
            checkStorage();
            console.log('✅ 环境检测完成');
        });
    </script>
</body>
</html>
