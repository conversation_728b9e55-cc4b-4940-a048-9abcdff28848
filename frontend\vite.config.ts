import { defineConfig, loadEnv, ConfigEnv, UserConfig } from "vite";
import { resolve } from "path";
import { wrapperEnv } from "./build/getEnv";
import { createProxy } from "./build/proxy";
import { createVitePlugins } from "./build/plugins";
import { visualizer } from "rollup-plugin-visualizer";
import pkg from "./package.json";
import dayjs from "dayjs";
import Components from "unplugin-vue-components/vite";
import AutoImport from "unplugin-auto-import/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

const { dependencies, devDependencies, name } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name },
  lastBuildTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
};

// @see: https://vitejs.dev/config/
export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);

  return {
    base: viteEnv.VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
        "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
        "async-validator": resolve("node_modules/async-validator/dist-node/index.js")
      }
    },
    define: {
      __APP_INFO__: JSON.stringify(__APP_INFO__)
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler" // or "modern"
        }
      }
    },
    optimizeDeps: {
      include: [
        // 核心依赖预构建
        "vue",
        "vue-router",
        "pinia",
        "element-plus",
        "element-plus/es",
        "element-plus/es/components/button/style/css",
        "element-plus/es/components/input/style/css",
        "element-plus/es/components/form/style/css",
        "element-plus/es/components/table/style/css",
        "element-plus/es/components/dialog/style/css",
        "element-plus/es/components/message/style/css",
        "element-plus/es/components/loading/style/css",
        // 常用工具库
        "axios",
        "dayjs",
        "dayjs/locale/zh-cn",
        "lodash",
        "lodash/debounce",
        "lodash/throttle",
        "lodash/cloneDeep",
        "@vueuse/core",
        "@vueuse/core/index",
        // 图标库
        "@element-plus/icons-vue",
        "@iconify/vue",
        // 其他常用依赖
        "nprogress",
        "mitt",
        "qs",
        "crypto-js",
        "md5"
      ],
      // 排除不需要预构建的依赖
      exclude: ["@iconify/json", "highlight.js", "echarts", "@antv/x6", "@antv/g2plot", "vue-cropper", "print-js"],
      // 强制预构建，开发时提升速度
      force: false,
      // 设置预构建缓存目录
      cacheDir: "node_modules/.vite",
      // 预构建入口
      entries: ["src/main.ts", "src/App.vue"]
    },
    server: {
      host: "0.0.0.0",
      port: viteEnv.VITE_PORT,
      open: viteEnv.VITE_OPEN,
      cors: true,
      // 启用HTTP/2
      https: false,
      // 预热文件，提升首次访问速度
      warmup: {
        clientFiles: ["./src/main.ts", "./src/App.vue", "./src/routers/index.ts", "./src/stores/index.ts"]
      },
      // 文件系统缓存
      fs: {
        // 允许访问工作区根目录之外的文件
        strict: false,
        // 缓存策略
        cachedChecks: true
      },
      // Load proxy configuration from .env.development
      proxy: createProxy(viteEnv.VITE_PROXY)
    },
    plugins: [
      ...createVitePlugins(viteEnv),
      // 自动引入 Element Plus 组件
      Components({
        resolvers: [
          ElementPlusResolver({
            // 禁用自动样式导入，避免重复优化
            importStyle: false
          })
        ]
      }),
      // 自动引入 Element Plus API
      AutoImport({
        resolvers: [ElementPlusResolver()]
      }),
      visualizer({
        open: true,
        gzipSize: true,
        brotliSize: true,
        filename: "dist/stats.html"
      })
    ],
    esbuild: {
      pure: viteEnv.VITE_DROP_CONSOLE ? ["console.log", "debugger"] : []
    },
    build: {
      outDir: "dist",
      minify: "esbuild",
      sourcemap: false,
      // 禁用 gzip 压缩大小报告，可略微减少打包时间
      reportCompressedSize: false,
      // 规定触发警告的 chunk 大小
      chunkSizeWarningLimit: 2000,
      // 构建缓存
      emptyOutDir: true,
      // 启用CSS代码分割
      cssCodeSplit: true,
      // 静态资源处理
      assetsInlineLimit: 4096, // 4kb以下的资源内联为base64
      rollupOptions: {
        // 缓存优化
        cache: true,
        // 外部依赖，不打包进bundle
        external: [],
        output: {
          // 优化代码分割策略，增强缓存效果
          chunkFileNames: chunkInfo => {
            // 为不同类型的chunk使用不同的命名策略
            if (chunkInfo.name?.includes("vendor")) {
              return "assets/vendor/[name]-[hash].js";
            }
            if (chunkInfo.name?.includes("async")) {
              return "assets/async/[name]-[hash].js";
            }
            return "assets/chunks/[name]-[hash].js";
          },
          entryFileNames: "assets/entry/[name]-[hash].js",
          assetFileNames: assetInfo => {
            // 根据文件类型分类存放
            const extType = assetInfo.name?.split(".").pop() || "";
            if (["png", "jpg", "jpeg", "gif", "svg", "webp"].includes(extType)) {
              return "assets/images/[name]-[hash].[ext]";
            }
            if (["woff", "woff2", "ttf", "eot"].includes(extType)) {
              return "assets/fonts/[name]-[hash].[ext]";
            }
            if (["css"].includes(extType)) {
              return "assets/styles/[name]-[hash].[ext]";
            }
            return "assets/[ext]/[name]-[hash].[ext]";
          },
          // 手动分割代码块，优化缓存策略
          manualChunks: id => {
            // 第三方库分离，提升缓存效果
            if (id.includes("node_modules")) {
              // 核心框架库 - 变化频率低，单独缓存
              if (id.includes("vue") || id.includes("pinia") || id.includes("vue-router")) {
                return "vue-vendor";
              }
              // UI组件库 - 变化频率低
              if (id.includes("element-plus")) {
                return "element-vendor";
              }
              // 图标库 - 按需加载
              if (id.includes("@element-plus/icons-vue") || id.includes("@iconify")) {
                return "icons-vendor";
              }
              // 工具库 - 变化频率中等
              if (id.includes("axios") || id.includes("dayjs") || id.includes("lodash") || id.includes("@vueuse")) {
                return "utils-vendor";
              }
              // 图表库 - 大体积，延迟加载
              if (id.includes("echarts") || id.includes("@antv")) {
                return "charts-vendor";
              }
              // 代码高亮 - 延迟加载
              if (id.includes("highlight.js")) {
                return "highlight-vendor";
              }
              // 其他第三方库
              return "vendor";
            }
            // 业务代码按模块分离
            if (id.includes("/src/views/")) {
              const match = id.match(/\/src\/views\/([^\/]+)/);
              if (match) {
                return `views-${match[1]}`;
              }
            }
            if (id.includes("/src/components/")) {
              return "components";
            }
            if (id.includes("/src/utils/")) {
              return "utils";
            }
            if (id.includes("/src/stores/")) {
              return "stores";
            }
          },
          // 优化模块导入
          globals: {
            vue: "Vue"
          }
        }
      }
    }
  };
});
