import { defineConfig, loadEnv, ConfigEnv, UserConfig } from "vite";
import { resolve } from "path";
import { wrapperEnv } from "./build/getEnv";
import { createProxy } from "./build/proxy";
import { createVitePlugins } from "./build/plugins";
import { visualizer } from "rollup-plugin-visualizer";
import pkg from "./package.json";
import dayjs from "dayjs";
import Components from "unplugin-vue-components/vite";
import AutoImport from "unplugin-auto-import/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

const { dependencies, devDependencies, name } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name },
  lastBuildTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
};

// @see: https://vitejs.dev/config/
export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);

  // 检测是否为Electron环境
  const isElectron = process.env.npm_lifecycle_event?.includes("electron") || process.env.ELECTRON === "true";

  return {
    base: viteEnv.VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
        "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
        "async-validator": resolve("node_modules/async-validator/dist-node/index.js")
      }
    },
    define: {
      __APP_INFO__: JSON.stringify(__APP_INFO__)
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler" // or "modern"
        }
      }
    },
    optimizeDeps: {
      include: [
        // 核心依赖预构建
        "vue",
        "vue-router",
        "pinia",
        "element-plus",
        "element-plus/es",
        "element-plus/es/components/button/style/css",
        "element-plus/es/components/input/style/css",
        "element-plus/es/components/form/style/css",
        "element-plus/es/components/table/style/css",
        "element-plus/es/components/dialog/style/css",
        "element-plus/es/components/message/style/css",
        "element-plus/es/components/loading/style/css",
        // 常用工具库
        "axios",
        "dayjs",
        "dayjs/locale/zh-cn",
        "lodash",
        "lodash/debounce",
        "lodash/throttle",
        "lodash/cloneDeep",
        "@vueuse/core",
        "@vueuse/core/index",
        // 图标库
        "@element-plus/icons-vue",
        "@iconify/vue",
        // 其他常用依赖
        "nprogress",
        "mitt",
        "qs",
        "crypto-js",
        "md5"
      ],
      // 排除不需要预构建的依赖
      exclude: ["@iconify/json", "highlight.js", "echarts", "@antv/x6", "@antv/g2plot", "vue-cropper", "print-js"],
      // 强制预构建，开发时提升速度
      force: false
    },
    server: {
      host: "0.0.0.0",
      port: viteEnv.VITE_PORT,
      open: viteEnv.VITE_OPEN,
      cors: true,
      // 预热文件，提升首次访问速度（仅在非Electron环境下启用）
      ...(!isElectron && {
        warmup: {
          clientFiles: ["./src/main.ts", "./src/App.vue", "./src/routers/index.ts", "./src/stores/index.ts"]
        }
      }),
      // 文件系统缓存
      fs: {
        // 允许访问工作区根目录之外的文件
        strict: false,
        // 缓存策略
        cachedChecks: true
      },
      // Load proxy configuration from .env.development
      proxy: createProxy(viteEnv.VITE_PROXY)
    },
    plugins: [
      ...createVitePlugins(viteEnv),
      // 自动引入 Element Plus 组件
      Components({
        resolvers: [
          ElementPlusResolver({
            // 禁用自动样式导入，避免重复优化
            importStyle: false
          })
        ]
      }),
      // 自动引入 Element Plus API
      AutoImport({
        resolvers: [ElementPlusResolver()]
      }),
      visualizer({
        open: true,
        gzipSize: true,
        brotliSize: true,
        filename: "dist/stats.html"
      })
    ],
    esbuild: {
      pure: viteEnv.VITE_DROP_CONSOLE ? ["console.log", "debugger"] : []
    },
    build: {
      outDir: "dist",
      minify: "esbuild",
      sourcemap: false,
      // 禁用 gzip 压缩大小报告，可略微减少打包时间
      reportCompressedSize: false,
      // 规定触发警告的 chunk 大小
      chunkSizeWarningLimit: 2000,
      // 构建缓存
      emptyOutDir: true,
      // 启用CSS代码分割
      cssCodeSplit: true,
      // 静态资源处理
      assetsInlineLimit: 4096, // 4kb以下的资源内联为base64
      rollupOptions: {
        output: {
          // 简化的代码分割策略，避免Electron环境下的问题
          chunkFileNames: "assets/js/[name]-[hash].js",
          entryFileNames: "assets/js/[name]-[hash].js",
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
          // 手动分割代码块，优化缓存策略
          manualChunks: {
            // 核心框架
            "vue-vendor": ["vue", "vue-router", "pinia"],
            // UI组件库
            "element-vendor": ["element-plus"],
            // 图标库（延迟加载）
            "icons-vendor": ["@element-plus/icons-vue", "@iconify/vue"],
            // 工具库
            "utils-vendor": ["axios", "dayjs", "lodash"],
            // 图表库（延迟加载）
            "charts-vendor": ["echarts"],
            // 代码高亮（延迟加载）
            "highlight-vendor": ["highlight.js", "@highlightjs/vue-plugin"]
          }
        }
      }
    }
  };
});
